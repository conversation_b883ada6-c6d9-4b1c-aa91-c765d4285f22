import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt

# 导入你已经重命名的两个应用文件
# 假设它们现在叫做 sender_app.py 和 receiver_app.py
import sender_app
import receiver_app


# --- 主应用程序类 ---
class CombinedApp:
    def __init__(self, master):
        self.master = master
        self.master.title("422通讯收发一体化控制台")
        # 设置一个足够大的默认窗口尺寸以容纳两个界面
        self.master.geometry("2000x850")

        # 使用PanedWindow创建一个可拖拽分割的窗口
        self.paned_window = ttk.PanedWindow(self.master, orient=tk.HORIZONTAL)
        self.paned_window.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # --- 创建左侧面板 (发送端) ---
        # 创建一个Frame作为发送端GUI的容器
        sender_frame = ttk.Frame(self.paned_window, width=900)
        self.paned_window.add(sender_frame, weight=1)  # weight参数控制缩放比例

        # --- 创建右侧面板 (接收端) ---
        # 创建一个Frame作为接收端GUI的容器
        receiver_frame = ttk.Frame(self.paned_window, width=1100)
        self.paned_window.add(receiver_frame, weight=1)

        # --- 实例化两个GUI应用 ---
        # 关键步骤：将上面创建的Frame作为master传递给原始的GUI类
        # 这样，原始的GUI代码就会在这些Frame内部构建它们的界面
        print("正在初始化发送端UI...")
        self.sender_ui = sender_app.SenderGUI(master=sender_frame)
        print("发送端UI初始化完成。")

        print("正在初始化接收端UI...")
        self.receiver_ui = receiver_app.ReceiverGUI(master=receiver_frame)
        print("接收端UI初始化完成。")

        # 绑定主窗口的关闭事件
        self.master.protocol("WM_DELETE_WINDOW", self.on_main_closing)

    def on_main_closing(self):
        """
        自定义关闭逻辑，以确保两个应用的后台线程和资源都被正确释放。
        这里我们不直接调用 sender_ui.on_closing() 和 receiver_ui.on_closing()，
        因为它们内部会弹出自己的确认框并试图销毁自己的master Frame，
        这会导致不好的用户体验（弹出多个确认框）和潜在的冲突。

        所以，我们在这里统一处理关闭逻辑。
        """
        if messagebox.askokcancel("退出", "确定要退出整个应用程序吗? 这将停止所有发送和接收任务。"):
            try:
                # 1. 安全地关闭发送端
                print("正在关闭发送端...")
                # 调用其内部的逻辑来停止发送线程和断开串口连接
                self.sender_ui.sender.stop_sending()
                self.sender_ui.sender.disconnect()
                # 手动关闭其创建的matplotlib图形，防止内存泄漏
                for widgets in self.sender_ui.command_widgets.values():
                    if 'fig' in widgets and widgets['fig']:
                        plt.close(widgets['fig'])
                print("发送端已关闭。")

            except Exception as e:
                print(f"关闭发送端时发生错误: {e}")

            try:
                # 2. 安全地关闭接收端
                print("正在关闭接收端...")
                # 调用其内部的逻辑来停止接收线程
                self.receiver_ui.receiver.stop()
                # 手动关闭其创建的matplotlib图形
                for widgets in self.receiver_ui.plot_widgets.values():
                    if 'fig' in widgets and widgets['fig']:
                        plt.close(widgets['fig'])
                print("接收端已关闭。")

            except Exception as e:
                print(f"关闭接收端时发生错误: {e}")

            # 3. 销毁主窗口
            self.master.destroy()


def main():
    """主函数"""
    root = tk.Tk()
    app = CombinedApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()